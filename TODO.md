# TODO: Jupiter Aggregator Integration for Independent Liquidity Pool

## Project Goal
Create an independent liquidity pool that Jupiter aggregator can detect and route through, enabling trading on both Raydium and Jupiter platforms.

## Current Status ✅
The existing implementation in `src/service/complete_bonding_curve.rs` successfully handles:
- ✅ **Step ① Withdraw**: Withdrawing token/SOL pairs from bonding curve 
- ✅ **Step ② CLMM Pool Creation**: Creating Raydium CLMM pools using `raydium_amm_v3`
- ✅ **Step ③ Position Creation**: Adding liquidity positions to CLMM pools
- ✅ **Jupiter Price API**: Already integrated for SOL price fetching (`src/bin/index_sol_price.rs`)

## Missing Components for Jupiter Integration

### 1. **HIGH PRIORITY: Pool Metadata & Standards Compliance**

#### 1.1 Pool Discoverability Enhancement
- [ ] **Implement AMM ID creation mechanism**
  - Current: Pool created but may lack proper indexing metadata
  - Required: Ensure <PERSON> can discover the pool through automated indexing
  - **Why Required**: Jupiter's indexing system needs to be able to find and catalog new pools. Without proper AMM ID structure, pools may remain invisible to Jupiter's routing engine, defeating the entire purpose of creating independent pools
  - Location: Add to `src/service/complete_bonding_curve.rs:create_clmm_pool()`

#### 1.2 Freeze Authority Validation
- [ ] **Verify token freeze authority is disabled**
  - Current: Unknown if implemented
  - Required: Jupiter requires freeze authority to be disabled for base tokens
  - **Why Required**: Jupiter rejects tokens with freeze authority enabled as a security measure. If freeze authority is active, malicious token creators could freeze user funds, creating liability for Jupiter. This is a hard requirement for Jupiter inclusion
  - Location: Add validation in token creation process

#### 1.3 Minimum Balance Requirements
- [ ] **Ensure 0.6 SOL minimum remaining balance**
  - Current: May not be enforced
  - Required: Pool creator must retain minimum 0.6 SOL after pool creation
  - **Why Required**: Jupiter's pool creation standards require creators to maintain sufficient SOL for ongoing transaction fees and pool maintenance. Pools created by accounts with insufficient remaining balance are flagged as potentially abandoned or unsustainable
  - Location: Add validation in `create_clmm_pool()` function

### 2. **HIGH PRIORITY: Liquidity Thresholds**

#### 2.1 Minimum Liquidity Validation
- [ ] **Implement $500 minimum liquidity check**
  - Current: No validation against USD thresholds
  - Required: Must maintain $500 liquidity on both buy/sell sides
  - **Why Required**: Jupiter requires minimum $500 liquidity to ensure meaningful trading volume and prevent manipulation. Low liquidity pools can be easily manipulated and provide poor user experience with high slippage. This threshold ensures only serious, well-funded pools are included in routing
  - Location: Add to `src/service/complete_bonding_curve.rs`
  - Dependencies: Use existing Jupiter price API for USD conversion

#### 2.2 Price Impact Monitoring
- [ ] **Add 30% maximum price impact validation**
  - Current: No price impact calculations
  - Required: <30% impact on $500 positions for continued routing
  - **Why Required**: High price impact indicates poor liquidity and creates bad user experience. Jupiter automatically excludes pools with >30% price impact on $500 trades to protect users from excessive slippage. After the 14-day grace period, pools failing this test are removed from routing
  - Implementation: Create new monitoring service
  - Location: `src/service/price_impact_monitor.rs` (new file)

### 3. **MEDIUM PRIORITY: Enhanced Pool Management**

#### 3.1 Pool Status Tracking
- [ ] **Add pool status to database schema**
  - **Why Required**: Need to track Jupiter compliance status to proactively address issues before pools are excluded from routing. This enables automated monitoring and alerting to maintain continuous Jupiter integration
  - Extend `src/db/coins/schema.rs` with Jupiter-specific fields:
    - `jupiter_discoverable: bool`
    - `liquidity_threshold_met: bool`
    - `price_impact_compliant: bool`
    - `jupiter_indexed_at: Option<DateTime>`

#### 3.2 Pool Health Monitoring
- [ ] **Create automated pool health checker**
  - **Why Required**: Jupiter evaluates pools every 30 minutes. Without continuous monitoring, pools could silently fall below thresholds and be excluded from routing without notification. Automated monitoring enables proactive maintenance to keep pools Jupiter-compliant
  - Location: `src/service/pool_health_monitor.rs` (new file)
  - Schedule: Run every 30 minutes (matches Jupiter's evaluation cycle)
  - Functions:
    - Check liquidity thresholds
    - Calculate price impact
    - Monitor trading activity
    - Update database status

### 4. **MEDIUM PRIORITY: Network Configuration**

#### 4.1 Mainnet Program IDs
- [ ] **Update mainnet configuration in `src/constants.rs`**
  - Current: Placeholder mainnet program addresses
  - Required: Proper Raydium CLMM mainnet program IDs
  - **Why Required**: Current implementation uses devnet/placeholder addresses. Production deployment requires correct mainnet program IDs to interact with live Raydium CLMM contracts and ensure Jupiter can properly index and route through the pools
  - Update:
    ```rust
    "mainnet" => NetworkConfig {
        amm_program: "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8".to_string(),
        clmm_program: "CAMMCzo5YL8w4VFF8KVHrK22GGUsp5VTaW7grrKgrWqK".to_string(),
        market_program: "srmqPiDlqoPsLhWjfqV2pQy8CxoBnDwfJR9x1yNFWqc".to_string(),
        fee_destination: "7YttLkHDoNj9wyDur5pM1ejNaAvT9X4eqaYcHQqHYmT3".to_string(),
    }
    ```

### 5. **LOW PRIORITY: Jupiter API Integration**

#### 5.1 Token Registration API
- [ ] **Implement Jupiter token registration helper**
  - **Why Required**: While Jupiter automatically indexes new tokens, explicit registration can reduce discovery time from hours to minutes. For meme tokens where timing is critical for early adopters, faster Jupiter inclusion provides competitive advantage
  - Location: `src/service/jupiter_integration.rs` (new file)
  - Purpose: Programmatically notify Jupiter of new tokens
  - Note: Jupiter automatically indexes, but explicit registration may speed up discovery

#### 5.2 Pool Performance Analytics
- [ ] **Add Jupiter routing analytics**
  - **Why Required**: Visibility into Jupiter routing performance helps optimize pool parameters and identify issues. Tracking routing inclusion/exclusion patterns enables data-driven improvements to maintain better Jupiter integration
  - Track when pools appear in Jupiter routing
  - Monitor trading volume through Jupiter
  - Location: Extend existing Redis service in `src/service/redis_service.rs`

### 6. **DEVELOPMENT TASKS**

#### 6.1 Environment Configuration
- [ ] **Add Jupiter-specific environment variables**
  - **Why Required**: Jupiter integration requires API keys and specific configuration parameters. Environment variables enable flexible configuration across development, staging, and production environments without code changes
  - Update `.env.example`:
    ```bash
    # Jupiter Integration
    JUPITER_API_KEY=your_jupiter_api_key
    ENABLE_JUPITER_MONITORING=true
    LIQUIDITY_CHECK_INTERVAL=1800  # 30 minutes
    MIN_LIQUIDITY_USD=500
    MAX_PRICE_IMPACT_BPS=3000      # 30%
    ```

#### 6.2 Testing & Validation
- [ ] **Create integration tests for Jupiter compatibility**
  - **Why Required**: Jupiter compatibility is complex with multiple requirements. Automated tests prevent regressions and validate that pools meet all Jupiter criteria before production deployment, reducing risk of failed integrations
  - Location: `tests/jupiter_integration_test.rs` (new file)
  - Test pool discovery simulation
  - Validate liquidity thresholds
  - Test price impact calculations

#### 6.3 Monitoring & Alerting
- [ ] **Add Jupiter integration monitoring**
  - **Why Required**: Silent failures in Jupiter integration could result in lost trading volume and revenue. Real-time monitoring and alerting enables immediate response to issues, maintaining continuous Jupiter availability for users
  - Alert when pools fall below liquidity thresholds
  - Monitor Jupiter indexing status
  - Track routing inclusion/exclusion events

### 7. **DOCUMENTATION UPDATES**

#### 7.1 Update CLAUDE.md
- [ ] **Add Jupiter integration commands and architecture**
  - **Why Required**: Future developers and Claude Code instances need comprehensive documentation to maintain and extend Jupiter integration features. Without proper documentation, complex Jupiter requirements could be misunderstood or forgotten
  - Document new environment variables
  - Add monitoring commands
  - Update architecture section with Jupiter components

#### 7.2 API Documentation
- [ ] **Document Jupiter-compatible pool creation process**
  - **Why Required**: Operators need clear instructions for setting up and troubleshooting Jupiter integration. Detailed documentation reduces support burden and enables successful deployments by non-technical team members
  - Update README.md with Jupiter-specific setup
  - Add troubleshooting guide for pool discovery issues

## Implementation Priority Order

1. **Week 1**: Liquidity validation & price impact monitoring (#2.1, #2.2)
2. **Week 2**: Pool metadata enhancements (#1.1, #1.2, #1.3)
3. **Week 3**: Database schema updates & health monitoring (#3.1, #3.2)
4. **Week 4**: Testing, documentation, and mainnet deployment (#6.1, #6.2)

## Success Criteria

- [ ] Pools created by the system appear in Jupiter routing within 24 hours
- [ ] Pools maintain >$500 liquidity and <30% price impact consistently
- [ ] Trading volume flows through both Raydium and Jupiter interfaces
- [ ] Automated monitoring prevents pool exclusion from Jupiter routing

## Technical Dependencies

- Existing `raydium_amm_v3` integration (✅ implemented)
- Jupiter Price API access (✅ implemented)
- Solana RPC access (✅ implemented)
- MongoDB for pool status tracking (✅ implemented)
- Redis for caching and monitoring (✅ implemented)

### 8. **DEPLOYMENT & PRODUCTION READINESS**

#### 8.1 Infrastructure Setup
- [ ] **Setup production Solana RPC endpoints**
  - **Why Required**: Devnet RPC endpoints have rate limits and reliability issues unsuitable for production. Mainnet requires dedicated RPC providers (Helius, Alchemy, or QuickNode) for consistent performance and higher rate limits
  - Current: Using public devnet endpoint (`https://api.devnet.solana.com`)
  - Required: Configure multiple mainnet RPC endpoints with failover
  - Location: Update `src/config.rs` with production RPC configuration

#### 8.2 Database & Redis Production Setup
- [ ] **Configure production MongoDB cluster**
  - **Why Required**: Production workload requires MongoDB with replication, backup, and monitoring. Single instance devnet setup will not handle production traffic or provide data safety guarantees
  - Setup MongoDB Atlas or self-hosted replica set
  - Configure automated backups
  - Add monitoring and alerting

- [ ] **Setup production Redis cluster**
  - **Why Required**: High-frequency pool monitoring and price caching requires Redis with persistence and high availability. Memory-only Redis instances risk data loss during restarts
  - Configure Redis clustering for high availability
  - Enable AOF persistence for price data
  - Setup Redis monitoring

#### 8.3 Security & Key Management
- [ ] **Implement secure private key management**
  - **Why Required**: Current implementation loads private keys from environment variables, which is insecure for production. Compromised keys could result in total loss of funds and pool control
  - Integrate with AWS KMS, Azure Key Vault, or HashiCorp Vault
  - Implement key rotation procedures
  - Add audit logging for all key usage

- [ ] **Setup production API key management**
  - **Why Required**: Jupiter API keys and other service credentials need secure storage and rotation capabilities. Hardcoded credentials are vulnerable to exposure in logs or source control
  - Secure storage for Jupiter API keys
  - Implement credential rotation
  - Add access control and monitoring

#### 8.4 Monitoring & Observability
- [ ] **Implement comprehensive application monitoring**
  - **Why Required**: Production systems require real-time visibility into performance, errors, and business metrics. Without proper monitoring, issues could go undetected leading to failed trades and revenue loss
  - Setup Datadog, New Relic, or Prometheus monitoring
  - Add custom metrics for Jupiter integration status
  - Configure alerting for critical errors and thresholds

- [ ] **Add distributed tracing**
  - **Why Required**: Multi-step pool creation process needs end-to-end tracing to diagnose failures. Complex interactions between Solana, Raydium, and Jupiter require detailed visibility for troubleshooting
  - Implement OpenTelemetry or Jaeger tracing
  - Trace pool creation workflows
  - Monitor external API call performance

#### 8.5 Performance & Scalability
- [ ] **Implement connection pooling**
  - **Why Required**: High-frequency monitoring and trading operations require efficient resource management. Without connection pooling, the system will suffer from connection overhead and potential rate limiting
  - Configure Solana RPC connection pooling
  - Optimize MongoDB connection management
  - Add Redis connection pooling

- [ ] **Setup horizontal scaling architecture**
  - **Why Required**: Meme token launches can create sudden traffic spikes requiring rapid scaling. Single-instance deployment cannot handle viral token launches that may generate thousands of simultaneous pool creation requests
  - Containerize applications with Docker
  - Setup Kubernetes or Docker Swarm orchestration
  - Configure auto-scaling based on metrics

#### 8.6 Disaster Recovery & Backup
- [ ] **Implement automated backup strategies**
  - **Why Required**: Pool data and transaction history are critical business assets. Data loss could result in inability to track token performance, lost revenue attribution, and compliance issues
  - Daily MongoDB backups with point-in-time recovery
  - Backup private keys and configuration
  - Test restore procedures regularly

- [ ] **Setup failover procedures**
  - **Why Required**: System downtime during token launches results in immediate revenue loss and damaged reputation. Automated failover ensures continuous service during infrastructure failures
  - Multi-region deployment capability
  - Database failover automation
  - RPC endpoint failover logic

#### 8.7 Compliance & Auditing
- [ ] **Implement comprehensive audit logging**
  - **Why Required**: Financial applications require detailed audit trails for compliance and forensic analysis. Regulatory requirements may demand transaction-level logging for anti-money laundering and tax reporting
  - Log all pool creation events
  - Track fund movements and transactions
  - Implement tamper-proof log storage

- [ ] **Setup compliance monitoring**
  - **Why Required**: Cryptocurrency regulations are evolving rapidly. Automated compliance monitoring helps ensure the platform remains compliant with changing requirements across jurisdictions
  - Monitor transaction patterns for suspicious activity
  - Implement KYC/AML checks where required
  - Setup regulatory reporting capabilities

#### 8.8 Load Testing & Performance Validation
- [ ] **Conduct comprehensive load testing**
  - **Why Required**: Meme token launches can generate massive traffic spikes that exceed normal capacity by 100x or more. Load testing ensures the system can handle viral events without degrading user experience
  - Test pool creation under high concurrency
  - Validate Jupiter API rate limit handling
  - Stress test database and caching layers

- [ ] **Performance benchmarking**
  - **Why Required**: Establish performance baselines to detect degradation over time. Performance issues during token launches can result in failed transactions and lost user confidence
  - Benchmark end-to-end pool creation time
  - Monitor Jupiter indexing latency
  - Track system resource utilization

## Estimated Development Time: 3-4 weeks + 2 weeks deployment

The current implementation has a solid foundation. The main gap is adding Jupiter-specific validation, monitoring, and compliance features rather than rebuilding the core pool creation functionality.