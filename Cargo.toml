[package]
name = "print-meme-collector-sol"
version = "0.1.0"
edition = "2021"

[dependencies]
anchor-lang = "=0.29.0"
anyhow = { version = "1.0" }
base64 = { version = "0.22" }
bs58 = { version = "0.5" }
clap = { version = "4.5", features = ["derive", "env"] }
dotenv = { version = "0.15" }
solana-client = "<2.0.0"
solana-sdk = "<2.0.0"
solana-transaction-status = "<2.0.0"
tokio = { version = "1.43", features = ["full"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
mongodb = { version = "2.1" }
bson = { version = "2", features = ["chrono-0_4"] }
futures = "0.3.5"
async-trait = "0.1.77"
serde = { version = "1.0", features = ["derive"] }
strum_macros = "0.26.3"
strum = "0.26.3"
serde_json = "1.0.138"
reqwest = { version = "0.12.4", features = ["json"] }
solana-program = "1.16.27"
spl-token = "4.0"
chrono = { version = "0.4.23" }
# redis as pool
bb8 = "0.9"
bb8-redis = "0.18"
redis = "0.27"
cached = { version = "0.54", features = ["async"] }
socketio-rust-emitter = { git = "https://github.com/epli2/socketio-rust-emitter.git" }
bigdecimal = "0.4"
raydium_amm = { git = "https://github.com/raydium-io/raydium-amm", default-features = false, features = [
    "client",
    "no-entrypoint",
] }
raydium-amm-v3 = { git = "https://github.com/raydium-io/raydium-clmm", features = [
    "client",
    "no-entrypoint",
] }
spl-associated-token-account = { version = "2.2.0", features = [
    "no-entrypoint",
] }
serum_dex = { version = "0.5.10", git = "https://github.com/raydium-io/openbook-dex", default-features = false, features = [
    "client",
    "no-entrypoint",
] }
safe-transmute = "0.11.2"
anchor-spl = { version = "=0.29.0", features = ["memo", "metadata"] }
anchor-client = { version = "=0.29.0", features = ["async"] }
spl-token-2022 = { version = "=0.9.0", features = ["no-entrypoint"] }
solana-account-decoder = "<1.17.0"
rand = "0.7.3"
borsh = "0.10"

