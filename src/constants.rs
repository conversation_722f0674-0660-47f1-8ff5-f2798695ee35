pub const SOLANA_DECIMALS: u8 = 9;
pub const NATIVE_SOL_ADDRESS: &str = "So11111111111111111111111111111111111111112";

#[derive(Debug)]
pub struct NetworkConfig {
    pub amm_program: String,
    pub clmm_program: String,
    pub market_program: String,
    pub fee_destination: String,
}

impl NetworkConfig {
    pub fn from_network(network: &str) -> Self {
        match network {
            "devnet" => NetworkConfig {
                amm_program: "HWy1jotHpo6UqeQxx49dpYYdQB8wj9Qk9MdxwjLvDHB8".to_string(),
                clmm_program: "devi51mZmdwUJGU9hjN27vEz64Gps7uUefqxg27EAtH".to_string(),
                market_program: "EoTcMgcDRTJVZDMZWBoU6rhYHZfkNTVEAfz3uUJRcYGj".to_string(),
                fee_destination: "3XMrhbv989VxAMi3DErLV9eJht1pHppW5LbKxe9fkEFR".to_string(),
            },
            "mainnet" => NetworkConfig {
                amm_program: "Mainnet_AMM_Program_Address".to_string(),
                clmm_program: "Mainnet_CLMM_Program_Address".to_string(),
                market_program: "Mainnet_Market_Program_Address".to_string(),
                fee_destination: "Mainnet_Fee_Destination_Address".to_string(),
            },
            _ => panic!("Unsupported network: {}", network),
        }
    }
}
