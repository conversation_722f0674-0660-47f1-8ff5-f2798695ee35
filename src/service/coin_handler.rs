use std::str::FromStr;

use bigdecimal::BigDecimal;
use bson::{doc, Decimal128};
use mongodb::options::{FindOneAndUpdateOptions, ReturnDocument, UpdateOptions};
use serde_json::json;

use anchor_lang::prelude::*;
use solana_sdk::pubkey::Pubkey;

use crate::{
    constants::NATIVE_SOL_ADDRESS,
    db::{coins::EListingStatusType, params::Params, shared_traits::RepositorySharedMethod},
    shared::{COINS_REPOSITORY, PARAMS_REPOSITORY, TOKEN_PRICES_REPOSITORY},
    types::{PubCreatedCoins, PubUpdatedCoins},
    utils::{calculate_bonding_curve_progress, get_market_cap_sol, ToF64},
};

use super::{redis_service::RedisEmitter, trade_handler::TradeEvent};

pub async fn handle_create_event_for_coin(
    event: &CreateEvent,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let result = find_params().await;
    let Ok(params) = result else {
        tracing::error!("{:?}", result.err());
        return Ok(());
    };

    let result = COINS_REPOSITORY
        .get_collection()
        .update_one(
            doc! {
                "tokenAddress": &event.mint.to_string(),
            },
            doc! {
                "$set": {
                    "tokenAddress": &event.mint.to_string(),
                    "bondingCurveAddress": &event.bonding_curve.to_string(),
                    "name": &event.name,
                    "symbol": &event.symbol,
                    "logoUri": &event.uri,
                    "creatorAddress": &event.user.to_string(),
                    "mcap": Decimal128::from_str("0").unwrap(),
                    "mcapUsd": Decimal128::from_str("0").unwrap(),
                    "symbol": event.symbol.clone(),
                    "logoUri": event.uri.clone(),
                    "creatorAddress": event.user.to_string(),
                    "virtualSolReserves": params.initial_virtual_sol_reserves,
                    "virtualTokenReserves": params.initial_virtual_sol_reserves,
                    "realSolReserves": Decimal128::from_str("0").unwrap(),
                    "realTokenReserves": params.initial_real_token_reserves,
                    "bondingCurve": 0.0,
                    "listedStatus": EListingStatusType::Unmigrated.to_string(),
                },
            },
            UpdateOptions::builder().upsert(true).build(),
        )
        .await;

    match result {
        Ok(opt_coin) => {
            if opt_coin.matched_count == 0 {
                tracing::error!(
                    "Failed to update database for coin {}: {}",
                    &event.mint.to_string(),
                    "Coin not found"
                );
                return Ok(());
            }

            tracing::info!(
                "Coin info updated for: coin={}, data={:?}",
                &event.mint.to_string(),
                &opt_coin
            );
        }
        Err(e) => {
            tracing::error!(
                "Failed to update database for coin {}: {:?}",
                &event.mint.to_string(),
                e
            );
            return Ok(());
        }
    }

    // Send create coin event to ws server
    let pub_coin = PubCreatedCoins {
        token_address: event.mint.to_string(),
        bonding_curve_address: event.bonding_curve.to_string(),
        name: event.name.clone(),
        symbol: event.symbol.clone(),
        logo_uri: event.uri.clone(),
        creator_address: event.user.to_string(),
        virtual_sol_reserves: params
            .initial_virtual_sol_reserves
            .to_string()
            .parse::<u64>()
            .unwrap(),
        virtual_token_reserves: params
            .initial_virtual_sol_reserves
            .to_string()
            .parse::<u64>()
            .unwrap(),
        real_token_reserves: params
            .initial_real_token_reserves
            .to_string()
            .parse::<u64>()
            .unwrap(),
    };

    if let Err(e) = emit_create_coin_event(pub_coin, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    Ok(())
}

pub async fn handle_trade_event_for_coin(
    event: &TradeEvent,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let token_address = event.mint.to_string();
    let result = find_params().await;
    let Ok(params) = result else {
        tracing::error!("{:?}", result.err());
        return Ok(());
    };

    let mcap = get_market_cap_sol(
        event.virtual_token_reserves,
        event.virtual_sol_reserves,
        params.token_total_supply,
    );

    let bonding_curve_progress = calculate_bonding_curve_progress(
        event.real_token_reserves,
        params
            .initial_real_token_reserves
            .to_string()
            .parse::<u64>()
            .unwrap(),
    );

    let sol_price_usd = TOKEN_PRICES_REPOSITORY
        .get_cached_token_price(NATIVE_SOL_ADDRESS)
        .await?;

    let bg_sol_price_usd = BigDecimal::from_str(&sol_price_usd.to_string()).unwrap();
    let bg_mcap = BigDecimal::from_str(&mcap.to_string()).unwrap();
    let mcap_usd = (bg_mcap * bg_sol_price_usd).to_f64();

    let update_object = doc! {
        "realSolReserves": Decimal128::from_str(&event.real_sol_reserves.to_string()).unwrap(),
        "realTokenReserves": Decimal128::from_str(&event.real_token_reserves.to_string()).unwrap(),
        "virtualSolReserves": Decimal128::from_str(&event.virtual_sol_reserves.to_string()).unwrap(),
        "virtualTokenReserves": Decimal128::from_str(&event.virtual_token_reserves.to_string()).unwrap(),
        "mcap": Decimal128::from_str(&mcap.to_string()).unwrap(),
        "mcapUsd": Decimal128::from_str(&mcap_usd.to_string()).unwrap(),
        "bondingCurve": bonding_curve_progress,
    };

    let pub_updated_coin = PubUpdatedCoins {
        mcap,
        mcap_usd,
        virtual_sol_reserves: event.virtual_sol_reserves,
        virtual_token_reserves: event.virtual_token_reserves,
        real_sol_reserves: event.real_sol_reserves,
        real_token_reserves: event.real_token_reserves,
        bonding_curve_progress,
        token_address: token_address.clone(),
    };

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &token_address,
            },
            vec![
                doc! {
                    "$set": {
                        "prevMcap": "$mcap",
                    }
                },
                doc! {
                    "$set": update_object.clone(),
                },
            ],
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    let Ok(opt_coin) = result else {
        tracing::error!(
            "Failed to update database for coin {}: {:?}",
            &token_address,
            result.err()
        );
        return Ok(());
    };

    if opt_coin.is_none() {
        tracing::error!(
            "Failed to update database for coin {}: {}",
            &token_address,
            "Coin not found"
        );
        return Ok(());
    }

    tracing::info!(
        "Coin info updated for: coin={}, data={:?}",
        &token_address,
        &update_object
    );

    // Send mcap change event to ws server
    let coin = opt_coin.unwrap();
    let mcap_change = if let Some(prev_mcap) = coin.prev_mcap {
        (BigDecimal::from_str(&mcap.to_string()).unwrap()
            - BigDecimal::from_str(&prev_mcap.to_string()).unwrap())
        .to_f64()
    } else {
        mcap
    };

    if let Err(e) = emit_mcap_change_event(mcap_change, &token_address, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    // Send new mcap event to ws server
    if let Err(e) = emit_updated_mcap_event(
        &mcap.to_string(),
        pub_updated_coin.mcap_usd,
        &token_address,
        redis_emitter,
    ) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    // Send update coin event to ws server
    if let Err(e) = emit_update_coin_event(pub_updated_coin, &token_address, redis_emitter) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    Ok(())
}

pub async fn handle_complete_event_for_coin(event: &CompleteEvent) -> anyhow::Result<()> {
    let update_object = doc! {
        "listedStatus": EListingStatusType::Migrating.to_string(),
    };

    let result = COINS_REPOSITORY
        .get_collection()
        .find_one_and_update(
            doc! {
                "tokenAddress": &event.mint.to_string(),
            },
            doc! {
                "$set": update_object.clone(),
            },
            FindOneAndUpdateOptions::builder()
                .return_document(ReturnDocument::After)
                .build(),
        )
        .await;

    let Ok(opt_coin) = result else {
        tracing::error!(
            "Failed to update database for coin {}: {:?}",
            &event.mint.to_string(),
            result.err()
        );
        return Ok(());
    };

    if opt_coin.is_none() {
        tracing::error!(
            "Failed to update database for coin {}: {}",
            &event.mint.to_string(),
            "Coin not found"
        );
        return Ok(());
    }

    tracing::info!(
        "Coin info updated for: coin={}, data={}",
        &event.mint.to_string(),
        &update_object
    );
    Ok(())
}

async fn find_params() -> anyhow::Result<Params, anyhow::Error> {
    let result = PARAMS_REPOSITORY.find_one(doc! {}, None).await;

    let Ok(opt_params) = result else {
        return Err(anyhow::anyhow!("Failed to find params: {:?}", result.err()));
    };

    let Some(params) = opt_params else {
        return Err(anyhow::anyhow!(
            "Failed to find params: {}",
            "Params not found"
        ));
    };
    Ok(params)
}

fn emit_create_coin_event(
    coin: PubCreatedCoins,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(&coin)?;
    let room = "SUBSCRIBE_NEW_COIN";
    let event = "CreatedCoin";

    redis_emitter.emit_room(room, event, &pub_data);

    Ok(())
}

fn emit_mcap_change_event(
    mcap_change: f64,
    token_address: &str,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(&json!({
        "tokenAddress": token_address,
        "mcapChange": mcap_change
    }))?;
    let room = "SUBSCRIBE_MCAP";
    let event = "McapChange";

    redis_emitter.emit_room(room, event, &pub_data);
    Ok(())
}

fn emit_updated_mcap_event(
    mcap: &str,
    mcap_usd: f64,
    token_address: &str,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(&json!({
        "mcap": mcap,
        "mcapUsd": mcap_usd,
    }))?;
    let room = format!("SUBSCRIBE_MCAP::{}", token_address);
    let event = "UpdatedMcap";

    redis_emitter.emit_room(&room, event, &pub_data);
    Ok(())
}

fn emit_update_coin_event(
    coin: PubUpdatedCoins,
    token_address: &str,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(&coin)?;
    let room = format!("SUBSCRIBE_COIN::{}", token_address);
    let event = "UpdatedCoin";

    redis_emitter.emit_room(&room, event, &pub_data);
    Ok(())
}

/// CreateEvent
#[event]
#[derive(Debug)]
pub struct CreateEvent {
    pub name: String,
    pub symbol: String,
    pub uri: String,
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
    pub user: Pubkey,
}

/// CompleteEvent
#[event]
#[derive(Debug)]
pub struct CompleteEvent {
    pub user: Pubkey,
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
    pub timestamp: i64,
    pub sol_amount: u64,
    pub token_amount: u64,
}

/// WithdrawCreatorFeeEvent
#[event]
#[derive(Debug)]
/// Withdraw creator fee event
pub struct WithdrawCreatorFeeEvent {
    /// sol_amount
    pub sol_amount: u64,
    /// creator
    pub creator: Pubkey,
    /// creator
    pub mint: Pubkey,
}
