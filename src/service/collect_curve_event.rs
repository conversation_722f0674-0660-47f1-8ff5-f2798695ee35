use std::time::Duration;

use anchor_lang::{prelude::*, Discriminator};
use event::EVENT_IX_TAG_LE;
use futures::future::try_join_all;
use solana_client::{
    rpc_client::GetConfirmedSignaturesForAddress2Config, rpc_config::RpcTransactionConfig,
};
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::{pubkey::Pubkey, signature::Signature};
use solana_transaction_status::EncodedConfirmedTransactionWithStatusMeta;
use solana_transaction_status::{
    option_serializer::OptionSerializer, UiInnerInstructions, UiInstruction, UiTransactionEncoding,
};
use tokio::time::sleep;

use super::coin_handler::{handle_trade_event_for_coin, CompleteEvent, CreateEvent};
use super::complete_bonding_curve::handle_complete_bonding_curve;
use super::holder_handler::handle_trade_event_for_holder;
use super::redis_service::RedisEmitter;
use super::trade_handler::{handle_trade_event, TradeEvent};
use super::trigger_airdrop::{handle_add_point_for_user, handle_trigger_airdrop};

use crate::db::latest_signatures::EServiceName;
use crate::service::coin_handler::{
    handle_complete_event_for_coin, handle_create_event_for_coin, WithdrawCreatorFeeEvent,
};
use crate::service::trigger_airdrop::handle_withdraw_creator_fee;
use crate::shared::LATEST_SIGNATURES_REPOSITORY;
use crate::{
    config::APP_CONFIG,
    shared::{PARAMS_REPOSITORY, SOLANA_CLIENT},
};

pub async fn collect_curve_event() -> anyhow::Result<()> {
    let redis_emitter = RedisEmitter::new()?;
    let program_id: Pubkey = APP_CONFIG.program_id.parse()?;
    let mut latest_signature = LATEST_SIGNATURES_REPOSITORY
        .find_one_by_service_name(&EServiceName::CollectCurveEvent)
        .await?
        .and_then(|record| record.signature.parse().ok());

    const SLEEP_DURATION: Duration = Duration::from_millis(100);
    const MAX_BATCH_SIZE: usize = 10;

    loop {
        let signatures = fetch_signatures_until(&program_id, latest_signature).await?;
        if signatures.is_empty() {
            sleep(SLEEP_DURATION).await;
            continue;
        }

        for batch in signatures.chunks(MAX_BATCH_SIZE) {
            let signature_transactions = try_join_all(batch.iter().map(|signature| async move {
                let transaction = SOLANA_CLIENT
                    .get_transaction_with_config(
                        signature,
                        RpcTransactionConfig {
                            encoding: Some(UiTransactionEncoding::Base64),
                            commitment: Some(CommitmentConfig::confirmed()),
                            ..Default::default()
                        },
                    )
                    .await?;
                Ok::<_, anyhow::Error>((signature, transaction))
            }))
            .await?;

            for (signature, transaction) in signature_transactions {
                each_signature(signature, &transaction, &redis_emitter).await?;
                LATEST_SIGNATURES_REPOSITORY
                    .upsert_latest_signature(
                        &EServiceName::CollectCurveEvent,
                        &signature.to_string(),
                    )
                    .await?;
            }
        }

        latest_signature = signatures.last().copied();
        sleep(SLEEP_DURATION).await;
    }
}

async fn each_signature(
    signature: &Signature,
    transaction: &EncodedConfirmedTransactionWithStatusMeta,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let slot = transaction.slot;
    let Some(metadata) = &transaction.transaction.meta else {
        return Ok(());
    };
    if metadata.err.is_some() {
        return Ok(());
    };
    let OptionSerializer::Some(inner_instructions) = metadata.inner_instructions.as_ref() else {
        return Ok(());
    };
    for (inner_index, inner_instruction) in inner_instructions.iter().enumerate() {
        each_inner_instructions(
            inner_index,
            inner_instruction,
            redis_emitter,
            TransactionInfo {
                block_time: transaction.block_time,
                signature_str: signature.to_string(),
                slot,
            },
        )
        .await?;
    }

    Ok(())
}

async fn each_inner_instructions(
    inner_index: usize,
    ix: &UiInnerInstructions,
    redis_emitter: &RedisEmitter,
    transaction_info: TransactionInfo,
) -> anyhow::Result<()> {
    let instructions = &ix.instructions;
    for instruction in instructions {
        let UiInstruction::Compiled(compiled_ix) = instruction else {
            continue;
        };
        if compiled_ix.data.is_empty() {
            continue;
        }

        let data = bs58::decode(compiled_ix.data.as_str()).into_vec()?;

        // TODO: event should be inserted or ignore
        // id of event may be tx_hash + inner_index + ix_index
        if let Some(event) = parse_event_cpi::<CreateEvent>(&data) {
            tracing::info!("handle_create_event_for_coin {} {:?}", inner_index, event);
            handle_create_event_for_coin(&event, redis_emitter).await?;
        }
        if let Some(event) = parse_event_cpi::<SetParamsEvent>(&data) {
            tracing::info!(
                "upsert_params_from_set_params_event {} {:?}",
                inner_index,
                event
            );
            PARAMS_REPOSITORY
                .upsert_params_from_set_params_event(event)
                .await?;
        }
        if let Some(event) = parse_event_cpi::<TradeEvent>(&data) {
            tracing::info!("handle_trade_event {} {:?}", inner_index, event);
            handle_trade_event(
                transaction_info.signature_str.clone(),
                transaction_info.slot,
                inner_index,
                &event,
                redis_emitter,
            )
            .await?;
            tracing::info!("handle_trade_event_for_coin {} {:?}", inner_index, event);
            handle_trade_event_for_coin(&event, redis_emitter).await?;
            tracing::info!("handle_trade_event_for_holder {} {:?}", inner_index, event);
            handle_trade_event_for_holder(&event, transaction_info.clone(), redis_emitter).await?;

            //handle_add_point for user
            tracing::info!(
                ">>>>>>>>>>>>>>>>>>>>>>handle_add_point_for_user {:?}",
                event
            );
            handle_add_point_for_user(&event).await?;
        }
        if let Some(event) = parse_event_cpi::<CompleteEvent>(&data) {
            let mint = event.mint;
            let sol_amount = event.sol_amount;

            tokio::spawn(async move {
                if let Err(e) = handle_complete_bonding_curve(&mint, sol_amount).await {
                    tracing::error!("Error handling complete bonding curve: {:?}", e.to_string());
                }
            });
            handle_complete_event_for_coin(&event).await?;
            // trigger_airdrop
            handle_trigger_airdrop(&mint).await?;
        }

        if let Some(event) = parse_event_cpi::<WithdrawCreatorFeeEvent>(&data) {
            tracing::info!(
                "handle_withdraw_creator_fee >>>>>>>>>>>>>>>>{} {:?}",
                inner_index,
                event
            );

            handle_withdraw_creator_fee(&event, transaction_info.signature_str.clone()).await?
        }
    }
    Ok(())
}

fn parse_event_cpi<E: AnchorDeserialize + Discriminator>(ix_data: &[u8]) -> Option<E> {
    if ix_data.len() < 16 {
        return None;
    }
    if ix_data[..8] == EVENT_IX_TAG_LE {
        let event_cpi = &ix_data[8..];
        let event_discriminator = &event_cpi[..8];
        if event_discriminator.eq(&E::discriminator()) {
            let event = E::try_from_slice(&event_cpi[8..]);
            return event.ok();
        }
    }
    None
}

/// old to new
/// until is exclusive
async fn fetch_signatures_until(
    program_id: &Pubkey,
    until: Option<Signature>,
) -> anyhow::Result<Vec<Signature>> {
    let mut before = None;
    let client = &SOLANA_CLIENT;
    let mut result = vec![];

    'outer: loop {
        // new to old
        let signatures = client
            .get_signatures_for_address_with_config(
                program_id,
                GetConfirmedSignaturesForAddress2Config {
                    before,
                    commitment: Some(CommitmentConfig::confirmed()),
                    ..Default::default()
                },
            )
            .await?;
        if signatures.is_empty() {
            break;
        }

        for signature in signatures {
            let signature: Signature = signature.signature.parse()?;

            if Some(signature) == before {
                continue;
            }
            if Some(signature) == until {
                // break parent loop
                break 'outer;
            }

            result.insert(0, signature);
            before = Some(signature);
        }
        sleep(Duration::from_millis(100)).await
    }

    Ok(result)
}

#[derive(Debug, Clone)]
pub struct TransactionInfo {
    pub block_time: Option<i64>,
    pub signature_str: String,
    pub slot: u64,
}

/// SetParamsEvent
#[event]
#[derive(Debug)]
pub struct SetParamsEvent {
    /// fee_recipient
    pub fee_recipient: Pubkey,
    /// initial_virtual_token_reserves
    pub initial_virtual_token_reserves: u64,
    /// initial_virtual_token_reserves
    pub initial_virtual_sol_reserves: u64,
    /// initial_real_token_reserves
    pub initial_real_token_reserves: u64,
    /// token_total_supply
    pub token_total_supply: u64,
    /// fee_basis_points
    pub fee_basis_points: u64,
}
