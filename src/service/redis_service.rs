use anyhow::Error;
use bb8::Pool;
use bb8_redis::RedisConnectionManager;
use chrono::Utc;
use redis::{AsyncCommands, Client};
use serde::{Deserialize, Serialize};
use socketio_rust_emitter::Emitter;
use tokio::sync::OnceCell as AsyncOnceCell;

use crate::config::APP_CONFIG;

type ConnectionPool = Pool<RedisConnectionManager>;

static REDIS_SERVICE: AsyncOnceCell<RedisService> = AsyncOnceCell::const_new();

pub struct RedisService {
    pub pool: ConnectionPool,
}

impl RedisService {
    pub async fn new() -> &'static RedisService {
        REDIS_SERVICE
            .get_or_init(|| async {
                let redis_url = if !APP_CONFIG.redis_password.is_empty() {
                    format!(
                        "redis://:{}@{}:{}/{}",
                        APP_CONFIG.redis_password,
                        APP_CONFIG.redis_host,
                        APP_CONFIG.redis_port,
                        APP_CONFIG.redis_db
                    )
                } else {
                    format!(
                        "redis://{}:{}/{}",
                        APP_CONFIG.redis_host, APP_CONFIG.redis_port, APP_CONFIG.redis_db
                    )
                };
                let manager = RedisConnectionManager::new(redis_url).unwrap();
                let pool = bb8::Pool::builder().build(manager).await.unwrap();
                RedisService { pool }
            })
            .await
    }

    fn get_sol_price_usd_key(&self) -> String {
        "print-meme:sol:price_usd".to_string()
    }

    pub async fn get_sol_price_usd(&self) -> Result<Option<String>, Error> {
        let key = self.get_sol_price_usd_key();
        let mut conn = self.pool.get().await?;
        let value: Option<String> = conn.get(&key).await?;

        Ok(value)
    }

    pub async fn set_sol_price_usd(
        &self,
        native_price_usd: String,
        expiration: u64,
    ) -> anyhow::Result<()> {
        let key = self.get_sol_price_usd_key();
        let mut conn: bb8::PooledConnection<'_, RedisConnectionManager> = self.pool.get().await?;
        let _: () = conn.set_ex(&key, native_price_usd, expiration).await?;

        Ok(())
    }
}

#[derive(Clone)]
pub struct RedisEmitter {
    pub emitter: Emitter,
}

impl RedisEmitter {
    pub fn new() -> anyhow::Result<Self> {
        let redis_url = if !APP_CONFIG.redis_password.is_empty() {
            format!(
                "redis://:{}@{}:{}/{}",
                APP_CONFIG.redis_password,
                APP_CONFIG.redis_host,
                APP_CONFIG.redis_port,
                APP_CONFIG.redis_db
            )
        } else {
            format!(
                "redis://{}:{}/{}",
                APP_CONFIG.redis_host, APP_CONFIG.redis_port, APP_CONFIG.redis_db
            )
        };
        let client = Client::open(redis_url)?;
        let emitter = Emitter::new(client);

        Ok(Self { emitter })
    }

    pub fn emit_room(&self, room: &str, event: &str, data: &str) {
        self.clone().emitter.to(room).emit(vec![event, data]);
    }
}