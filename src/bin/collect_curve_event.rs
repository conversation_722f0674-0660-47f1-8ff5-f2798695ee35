use print_meme_collector_sol::{
    config::APP_CONFIG, db::common::setup_mongodb,
    service::collect_curve_event::collect_curve_event, utils::init_standard_tracing,
};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;
    collect_curve_event().await?;
    Ok(())
}
