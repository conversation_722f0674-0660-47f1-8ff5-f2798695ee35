use anyhow::Context;
use print_meme_collector_sol::{
    config::APP_CONFIG, constants::NATIVE_SOL_ADDRESS, db::common::setup_mongodb,
    service::redis_service::RedisService, shared::TOKEN_PRICES_REPOSITORY,
    utils::init_standard_tracing,
};
use std::time::Duration;
use tokio::time::sleep;

async fn get_solana_price_usd() -> Result<f64, anyhow::Error> {
    tracing::info!("Fetching solana price in usd");
    let url = "https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd";
    let response = reqwest::get(reqwest::Url::parse(url)?)
        .await
        .with_context(|| format!("Failed to fetch data from {}", url))?;

    println!("response: {:?}", response);

    let body: serde_json::Value = response
        .json()
        .await
        .context("Failed to parse JSON response")?;

    let price = body["solana"]["usd"].as_f64();

    println!("price: {:?}", price);


    match price {
        Some(price) => Ok(price),
        None => Err(anyhow::anyhow!(format!("Failed to get price: {}", body))),
    }
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;
    let redis_service: &'static RedisService = RedisService::new().await;
    let sleep_duration = 30;

    loop {
        match get_solana_price_usd().await {
            Ok(price) => {
                redis_service
                    .set_sol_price_usd(price.to_string(), sleep_duration)
                    .await
                    .context("Failed to set SOL price in Redis")?;
                TOKEN_PRICES_REPOSITORY
                    .upsert_token_price(NATIVE_SOL_ADDRESS, price)
                    .await
                    .context("Failed to upsert SOL price in MongoDB")?;
            }
            Err(e) => {
                tracing::error!("{}", e);
            }
        };

        sleep(Duration::from_millis(1000 * sleep_duration)).await
    }
}
