use std::sync::LazyLock;

use clap::Parser;

pub static APP_CONFIG: LazyLock<Config> = LazyLock::new(Config::parse);

#[derive(Debug, Parser)]
pub struct Config {
    #[clap(long, env, default_value = "debug")]
    pub log_level: String,

    #[clap(long, env, default_value = "https://api.devnet.solana.com")]
    pub fullnode_url: String,

    #[clap(long, env)]
    pub program_id: String,

    #[clap(long, env)]
    pub stake_program_id: String,

    #[clap(long, env)]
    pub mongodb_uri: String,

    #[clap(long, env)]
    pub redis_host: String,

    #[clap(long, env)]
    pub redis_port: String,

    #[clap(long, env)]
    pub redis_password: String,

    #[clap(long, env)]
    pub redis_db: String,

    #[clap(long, env)]
    pub private_key: String,

    #[clap(long, env)]
    pub network: String,

    #[clap(long, env)]
    pub airdrop_private_key: String,

    #[clap(long, env)]
    pub rust_env: String, 

    #[clap(long, env)]
    pub admin_username: String,

    #[clap(long, env)]
    pub admin_pw: String,
    
}
